import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
// Import only the invoice model to avoid conflicts
import 'package:quarterlies/models/invoice.dart';
import 'package:quarterlies/models/customer.dart';
import 'package:quarterlies/models/job.dart';
import 'package:quarterlies/models/mileage.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

import 'package:quarterlies/utils/input_validators.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'invoice_item_form_screen.dart';

class InvoiceFormScreen extends StatefulWidget {
  final Invoice? invoice;
  final String? jobId; // Optional job ID if creating from job detail screen
  final String? contractId; // Optional contract ID if creating from contract

  const InvoiceFormScreen({
    super.key,
    this.invoice,
    this.jobId,
    this.contractId,
  });

  @override
  State<InvoiceFormScreen> createState() => _InvoiceFormScreenState();
}

class _InvoiceFormScreenState extends State<InvoiceFormScreen> {
  final VoiceRecordingService _voiceRecordingService = VoiceRecordingService();
  final _formKey = GlobalKey<FormState>();
  final _supabaseClient = Supabase.instance.client;
  final SupabaseService _supabaseService = SupabaseService();
  final DataRepository _dataRepository = DataRepository();

  // Form controllers
  late TextEditingController _notesController;

  // Form data
  String? _selectedJobId;
  String? _selectedCustomerId;
  DateTime _issueDate = DateTime.now();
  DateTime? _dueDate;
  List<InvoiceItem> _lineItems = [];
  bool _useDueDate = true;

  // For manual total amount override
  late TextEditingController _totalAmountController;
  bool _useManualTotal = false;

  // UI state
  bool _isRecording = false;
  bool _isOffline = false;
  String? _errorMessage;
  List<Job> _jobs = [];
  List<Customer> _customers = [];

  // Flags to track if there are available records for bulk actions
  bool _hasUninvoicedHours = false;
  bool _hasUninvoicedMileage = false;
  bool _hasUninvoicedExpenses = false;
  bool _hasOpenInvoiceHours = false;
  bool _hasOpenInvoiceMileage = false;
  bool _hasOpenInvoiceExpenses = false;

  // Selected job object
  Job? _selectedJob;
  // Voice note URL for the invoice
  String? _voiceNoteUrl;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _notesController = TextEditingController(text: widget.invoice?.notes ?? '');
    _totalAmountController = TextEditingController(
      text: widget.invoice?.totalAmount.toStringAsFixed(2) ?? '0.00',
    );
    _selectedJobId = widget.jobId ?? widget.invoice?.jobId;
    _useManualTotal =
        widget.invoice != null; // Use manual total for existing invoices

    // Initialize due date
    if (widget.invoice != null) {
      _dueDate = widget.invoice!.dueDate;
      _useDueDate =
          true; // If editing an invoice, always show the due date field
    } else {
      _loadDefaultDueDate();
    }

    _setupConnectivityListener();
    _loadFormData();
  }

  @override
  void dispose() {
    _notesController.dispose();
    _totalAmountController.dispose();
    _voiceRecordingService.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(
            context,
            'Back online. Data will sync automatically.',
          );
        } else {
          ErrorDisplay.showSync(
            context,
            'Working offline. Changes saved locally.',
            isOffline: true,
          );
        }
      }
    });
  }

  // Load default due date from job settings or user settings
  Future<void> _loadDefaultDueDate() async {
    try {
      // First check if we have a job ID and if that job has a specific due days setting
      if (_selectedJobId != null) {
        final job = await _supabaseService.getJobById(_selectedJobId!);
        if (job.defaultInvoiceDueDays != null) {
          setState(() {
            _dueDate = DateTime.now().add(
              Duration(days: job.defaultInvoiceDueDays!),
            );
          });
          return; // Job-specific setting found, no need to check user settings
        }
      }

      // Fall back to user settings if no job-specific setting exists
      final userSettings = await _supabaseService.getUserSettings();
      setState(() {
        _dueDate = DateTime.now().add(
          Duration(days: userSettings.defaultInvoiceDueDays),
        );
      });
    } catch (e) {
      // If there's an error, use 30 days as default
      setState(() {
        _dueDate = DateTime.now().add(const Duration(days: 30));
      });
    }
  }

  Future<void> _loadFormData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadInvoiceFormData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Load jobs
          final jobs = await _supabaseService.getJobs();

          // Load customers
          final customers = await _supabaseService.getCustomers();

          // If editing an existing invoice, load its line items
          if (widget.invoice != null) {
            _issueDate = widget.invoice!.issueDate;
            _dueDate = widget.invoice!.dueDate;
            _lineItems = widget.invoice!.lineItems ?? [];
            _selectedCustomerId = widget.invoice!.customerId;

            // For existing invoices, only sync costs if invoice is still in 'open' status
            // This prevents modifying invoices that have been paid or are in other final states
            bool canSyncCosts = widget.invoice!.status.toLowerCase() == 'open';

            // If job ID is provided, set the customer ID based on the job
            if (_selectedJobId != null && canSyncCosts) {
              final job = jobs.firstWhere((j) => j.id == _selectedJobId);
              _selectedJob = job;

              // If creating a new invoice and job has specific due days setting, update due date
              if (widget.invoice == null && job.defaultInvoiceDueDays != null) {
                _dueDate = DateTime.now().add(
                  Duration(days: job.defaultInvoiceDueDays!),
                );
              }

              // If any sync option is enabled and invoice is still open, update with latest costs
              if (job.syncExpenses || job.syncLaborCosts) {
                final jobItemsMap = await _supabaseService
                    .generateInvoiceItemsFromJob(_selectedJobId!);
                final List<InvoiceItem> availableItems =
                    (jobItemsMap['availableItems'] as List<dynamic>)
                        .cast<InvoiceItem>();

                if (availableItems.isNotEmpty) {
                  // Only update if there are actual changes to prevent unnecessary modifications
                  if (_shouldUpdateLineItems(_lineItems, availableItems)) {
                    // Preserve excluded status for existing items
                    final updatedItems =
                        availableItems.map((newItem) {
                          // Try to find matching item in existing line items
                          final existingItem = _lineItems.firstWhere(
                            (oldItem) =>
                                oldItem.type == newItem.type &&
                                oldItem.description == newItem.description &&
                                oldItem.unitPrice == newItem.unitPrice,
                            orElse: () => newItem,
                          );
                          // If found and excluded, keep it excluded
                          if (existingItem != newItem &&
                              existingItem.excluded == true) {
                            return newItem.copyWith(excluded: true);
                          }
                          return newItem;
                        }).toList();

                    _lineItems = updatedItems;
                    if (mounted) {
                      ErrorDisplay.showOperation(
                        context,
                        'Invoice updated with latest job costs',
                      );
                    }
                  }
                }
              }
            }
          } else {
            // If job ID is provided for a new invoice, set the customer ID based on the job
            if (_selectedJobId != null) {
              final job = jobs.firstWhere((j) => j.id == _selectedJobId);
              _selectedJob = job;
              _selectedCustomerId = job.customerId;

              // If creating a new invoice, check sync settings and load appropriate job costs
              // Using the selective sync settings
              if (job.syncExpenses ||
                  job.syncMileage ||
                  job.syncLaborCosts ||
                  job.syncEstimateItems) {
                final jobItemsMap = await _supabaseService
                    .generateInvoiceItemsFromJob(_selectedJobId!);
                final List<InvoiceItem> availableItems =
                    (jobItemsMap['availableItems'] as List<dynamic>)
                        .cast<InvoiceItem>();
                if (availableItems.isNotEmpty) {
                  _lineItems = availableItems;
                }
              }
            }

            // If contract ID is provided, load contract line items
            if (widget.contractId != null) {
              try {
                // Get the contract
                final contract = await _supabaseService.getContractById(
                  widget.contractId!,
                );

                // Set job and customer IDs from contract
                _selectedJobId = contract.jobId;
                _selectedCustomerId = contract.customerId;

                // Find the job
                final job = jobs.firstWhere((j) => j.id == _selectedJobId);
                _selectedJob = job;

                // Convert contract line items to invoice line items
                final contractLineItems =
                    contract.lineItems.map((contractItem) {
                      return InvoiceItem(
                        description: contractItem.description,
                        quantity: contractItem.quantity,
                        unitPrice: contractItem.unitPrice,
                        unit:
                            contractItem.unit ?? '', // Ensure unit is not null
                        type: 'contract',
                        sourceId: 'contract_${contract.id}',
                      );
                    }).toList();

                // Add contract line items to invoice
                _lineItems.addAll(contractLineItems);

                // Show success message
                if (mounted) {
                  ErrorDisplay.showOperation(
                    context,
                    'Contract line items added to invoice',
                  );
                }
              } catch (e) {
                debugPrint('Error loading contract: $e');
                // Continue without contract items
              }
            }
          }

          // Check for available records if a job is selected
          if (_selectedJobId != null && _selectedJob != null) {
            await _checkAvailableRecords();
          }

          setState(() {
            _jobs = jobs;
            _customers = customers;
          });
        },
        message: 'Loading invoice data...',
        errorMessage: 'Failed to load invoice data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load invoice data: ${e.toString()}';
        });
      }
    }
  }

  // Check for available records to determine which bulk action buttons to show
  Future<void> _checkAvailableRecords() async {
    if (_selectedJobId == null || _selectedJob == null) return;

    try {
      final jobItemsMap = await _supabaseService.generateInvoiceItemsFromJob(
        _selectedJobId!,
      );

      // Get available items
      final List<InvoiceItem> availableItems =
          (jobItemsMap['availableItems'] as List<dynamic>).cast<InvoiceItem>();

      // Get items in open invoices
      final List<InvoiceItem> openInvoiceItems =
          (jobItemsMap['inOpenInvoiceItems'] as List<dynamic>)
              .cast<InvoiceItem>();

      // Check for uninvoiced hours
      final uninvoicedHours =
          availableItems.where((item) => item.type == 'labor').toList();

      // Check for uninvoiced mileage
      final uninvoicedMileage =
          availableItems.where((item) => item.type == 'mileage').toList();

      // Check for uninvoiced expenses
      final uninvoicedExpenses =
          availableItems.where((item) => item.type == 'expense').toList();

      // Check for hours in open invoices
      final openInvoiceHours =
          openInvoiceItems.where((item) => item.type == 'labor').toList();

      // Check for mileage in open invoices
      final openInvoiceMileage =
          openInvoiceItems.where((item) => item.type == 'mileage').toList();

      // Check for expenses in open invoices
      final openInvoiceExpenses =
          openInvoiceItems.where((item) => item.type == 'expense').toList();

      setState(() {
        _hasUninvoicedHours = uninvoicedHours.isNotEmpty;
        _hasUninvoicedMileage = uninvoicedMileage.isNotEmpty;
        _hasUninvoicedExpenses = uninvoicedExpenses.isNotEmpty;
        _hasOpenInvoiceHours = openInvoiceHours.isNotEmpty;
        _hasOpenInvoiceMileage = openInvoiceMileage.isNotEmpty;
        _hasOpenInvoiceExpenses = openInvoiceExpenses.isNotEmpty;
      });
    } catch (e) {
      debugPrint('Error checking available records: $e');
      // If there's an error, assume no records are available
      setState(() {
        _hasUninvoicedHours = false;
        _hasUninvoicedMileage = false;
        _hasUninvoicedExpenses = false;
        _hasOpenInvoiceHours = false;
        _hasOpenInvoiceMileage = false;
        _hasOpenInvoiceExpenses = false;
      });
    }
  }

  // Helper method to determine if line items need updating
  bool _shouldUpdateLineItems(
    List<InvoiceItem> currentItems,
    List<InvoiceItem> newItems,
  ) {
    // Simple check - if the counts are different, update is needed
    if (currentItems.length != newItems.length) return true;

    // Compare items by type, description, and amount to detect changes
    for (final newItem in newItems) {
      bool found = false;
      for (final currentItem in currentItems) {
        if (newItem.type == currentItem.type &&
            newItem.description == currentItem.description &&
            (newItem.quantity * newItem.unitPrice -
                        currentItem.quantity * currentItem.unitPrice)
                    .abs() <
                0.01) {
          found = true;
          break;
        }
      }
      if (!found) {
        return true; // If any new item doesn't match existing items, update is needed
      }
    }

    return false; // No differences found
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedJobId == null) {
      ErrorDisplay.showWarning(context, 'Please select a job');
      return;
    }

    if (_lineItems.isEmpty) {
      ErrorDisplay.showWarning(context, 'Please add at least one line item');
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveInvoice',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Determine total amount - either use manual entry or calculate from line items
          double totalAmount;
          if (_useManualTotal && _totalAmountController.text.isNotEmpty) {
            totalAmount = double.tryParse(_totalAmountController.text) ?? 0.0;
          } else {
            // Calculate from line items, excluding any items marked as excluded
            totalAmount = _lineItems.fold<double>(
              0,
              (sum, item) =>
                  item.excluded == true
                      ? sum
                      : sum + (item.quantity * item.unitPrice),
            );
          }

          // Get customer ID from selected job if not already set
          if (_selectedCustomerId == null && _selectedJobId != null) {
            final job = _jobs.firstWhere((j) => j.id == _selectedJobId);
            _selectedCustomerId = job.customerId;
          }

          // Set due date based on user preference
          final DateTime effectiveDueDate =
              _useDueDate && _dueDate != null
                  ? _dueDate!
                  : _issueDate; // If no due date is set, use issue date as fallback

          // Create or update invoice
          final invoice = Invoice(
            id: widget.invoice?.id,
            userId:
                widget.invoice?.userId ?? _supabaseClient.auth.currentUser!.id,
            jobId: _selectedJobId!,
            customerId: _selectedCustomerId!,
            issueDate: _issueDate,
            dueDate: effectiveDueDate,
            totalAmount: totalAmount,
            amountPaid: widget.invoice?.amountPaid ?? 0.0,
            status: widget.invoice?.status ?? 'open',
            lineItems: _lineItems,
            notes: _notesController.text.trim(),
            voiceNoteUrl: _voiceNoteUrl,
            createdAt: widget.invoice?.createdAt,
            updatedAt: DateTime.now(),
          );

          if (widget.invoice == null) {
            // Use SupabaseService for adding invoices - convert to JSON first
            await _supabaseClient.from('invoices').insert(invoice.toJson());

            // Mark items as invoiced if needed
            // This would normally be handled by the SupabaseService._markItemsAsInvoiced method
          } else {
            // Use the updateInvoice method
            await _supabaseService.updateInvoice(invoice);
          }

          if (mounted) {
            // Show success feedback
            final operation = widget.invoice == null ? 'save' : 'update';
            ErrorDisplay.showDataOperation(
              context,
              'invoice',
              operation,
              isOffline: _isOffline,
            );
            Navigator.pop(context);
          }
        },
        message:
            widget.invoice == null
                ? 'Creating invoice...'
                : 'Updating invoice...',
        errorMessage: 'Failed to save invoice',
      );
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'saveInvoice',
            'isEditing': widget.invoice != null,
            'invoiceId': widget.invoice?.id,
            'jobId': _selectedJobId,
            'customerId': _selectedCustomerId,
            'lineItemsCount': _lineItems.length,
          },
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(
          context,
          appError,
          onRetry: () => _saveInvoice(),
        );
      }
    }
  }

  Future<void> _selectDate(BuildContext context, bool isIssueDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isIssueDate
              ? _issueDate
              : (_dueDate ?? DateTime.now().add(const Duration(days: 30))),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      setState(() {
        if (isIssueDate) {
          _issueDate = picked;
          // If due date is enabled, update it based on the new issue date
          if (_useDueDate && _dueDate != null) {
            // Try to maintain the same number of days between issue and due date
            final currentDiff = _dueDate!.difference(_issueDate).inDays;
            _dueDate = picked.add(Duration(days: currentDiff));
          }
        } else {
          _dueDate = picked;
        }
      });
    }
  }

  void _addLineItem() async {
    final result = await Navigator.push<InvoiceItem>(
      context,
      MaterialPageRoute(builder: (context) => const InvoiceItemFormScreen()),
    );

    if (result != null) {
      setState(() {
        _lineItems.add(result);
      });
    }
  }

  void _editLineItem(int index) async {
    final result = await Navigator.push<InvoiceItem>(
      context,
      MaterialPageRoute(
        builder: (context) => InvoiceItemFormScreen(item: _lineItems[index]),
      ),
    );

    if (result != null) {
      setState(() {
        _lineItems[index] = result;
      });
    }
  }

  void _removeLineItem(int index) {
    setState(() {
      _lineItems.removeAt(index);
    });
  }

  // Bulk action methods for adding uninvoiced items
  void _addAllUninvoicedHours() async {
    if (_selectedJobId == null) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'addUninvoicedHours',
        () async {
          final jobItemsMap = await _supabaseService
              .generateInvoiceItemsFromJob(_selectedJobId!);

          // We'll extract source IDs from the descriptions

          final List<InvoiceItem> availableItems =
              (jobItemsMap['availableItems'] as List<dynamic>)
                  .cast<InvoiceItem>();

          // Filter for labor items only
          final laborItems =
              availableItems.where((item) => item.type == 'labor').toList();

          if (laborItems.isNotEmpty) {
            // Track how many items were actually added (not already in the list)
            int addedCount = 0;

            setState(() {
              // Add labor items that aren't already in the line items
              for (final laborItem in laborItems) {
                bool alreadyExists = _lineItems.any(
                  (existingItem) =>
                      existingItem.type == laborItem.type &&
                      existingItem.description == laborItem.description &&
                      (existingItem.unitPrice - laborItem.unitPrice).abs() <
                          0.01 &&
                      (existingItem.quantity - laborItem.quantity).abs() < 0.01,
                );

                if (!alreadyExists) {
                  // Extract source ID from the description
                  String? sourceId;

                  // Description format: "Labor: Time log {id_substring} (Optional - from time tracking)"
                  // or "Labor: {notes} (Optional - from time tracking)"
                  final description = laborItem.description;
                  final timeLogIdMatch = RegExp(
                    r'Time log ([a-zA-Z0-9]{8})',
                  ).firstMatch(description);

                  if (timeLogIdMatch != null &&
                      timeLogIdMatch.groupCount >= 1) {
                    // This is the ID substring (first 8 chars)
                    final idSubstring = timeLogIdMatch.group(1);
                    // We'll use this as the source ID with a prefix
                    sourceId = 'timelog_$idSubstring';
                  }

                  // Add the item with the source ID
                  _lineItems.add(laborItem.copyWith(sourceId: sourceId));
                  addedCount++;
                }
              }

              if (mounted) {
                if (addedCount > 0) {
                  ErrorDisplay.showOperation(
                    context,
                    'Added $addedCount labor items',
                  );
                  // Recalculate the total amount
                  if (!_useManualTotal) {
                    double total = _lineItems.fold<double>(
                      0,
                      (sum, item) =>
                          item.excluded == true
                              ? sum
                              : sum + (item.quantity * item.unitPrice),
                    );
                    _totalAmountController.text = total.toStringAsFixed(2);
                  }
                } else {
                  ErrorDisplay.showInfo(
                    context,
                    'All available labor items are already in the invoice',
                  );
                }
              }
            });
          } else {
            if (mounted) {
              // Get all time logs for this job
              final allTimeLogs = await _supabaseService.getTimeLogsByJob(
                _selectedJobId!,
              );

              // Check if any are in paid invoices
              final paidTimeLogs =
                  allTimeLogs.where((log) => log.invoicedInId != null).toList();

              if (mounted) {
                if (paidTimeLogs.isNotEmpty &&
                    allTimeLogs.length == paidTimeLogs.length) {
                  // All time logs are in paid invoices
                  ErrorDisplay.showInfo(
                    context,
                    'All labor items for this job are already included in paid invoices',
                  );
                } else {
                  ErrorDisplay.showWarning(
                    context,
                    'No uninvoiced labor items available',
                  );
                }
              }
            }
          }
        },
        message: 'Adding uninvoiced hours...',
        errorMessage: 'Failed to add uninvoiced hours',
      );
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error loading labor items: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        // Update availability flags
        await _checkAvailableRecords();
      }
    }
  }

  void _addAllUninvoicedMileage() async {
    if (_selectedJobId == null) return;

    // Loading state managed by LoadingStateProvider

    try {
      final jobItemsMap = await _supabaseService.generateInvoiceItemsFromJob(
        _selectedJobId!,
      );

      // We'll extract source IDs from the descriptions

      final List<InvoiceItem> availableItems =
          (jobItemsMap['availableItems'] as List<dynamic>).cast<InvoiceItem>();

      // Filter for mileage items only
      final mileageItems =
          availableItems.where((item) => item.type == 'mileage').toList();

      if (mileageItems.isNotEmpty) {
        // Track how many items were actually added (not already in the list)
        int addedCount = 0;

        setState(() {
          // Add mileage items that aren't already in the line items
          for (final mileageItem in mileageItems) {
            bool alreadyExists = _lineItems.any(
              (existingItem) =>
                  existingItem.type == mileageItem.type &&
                  existingItem.description == mileageItem.description &&
                  (existingItem.unitPrice - mileageItem.unitPrice).abs() <
                      0.01 &&
                  (existingItem.quantity - mileageItem.quantity).abs() < 0.01,
            );

            if (!alreadyExists) {
              // Extract source ID from the description
              String? sourceId;

              // Description format: "Mileage: {description}" or
              // "Mileage: Total for {count} trips ({miles} miles)"
              final description = mileageItem.description;

              // For individual mileage entries
              if (description.startsWith('Mileage: ') &&
                  !description.contains('Total for')) {
                // Use a hash of the description as the source ID
                final hashCode = description.hashCode.abs().toString();
                sourceId = 'mileage_$hashCode';
              }

              // Add the item with the source ID
              _lineItems.add(mileageItem.copyWith(sourceId: sourceId));
              addedCount++;
            }
          }

          if (mounted) {
            if (addedCount > 0) {
              ErrorDisplay.showOperation(
                context,
                'Added $addedCount mileage items',
              );
            } else {
              ErrorDisplay.showInfo(
                context,
                'All available mileage items are already in the invoice',
              );
            }
          }
        });
      } else {
        if (mounted) {
          // Get all expenses for this job
          final allExpenses = await _supabaseService.getExpensesByJob(
            _selectedJobId!,
          );

          // Filter for mileage expenses - now using Mileage class
          final allMileage = allExpenses.whereType<Mileage>().toList();

          // Check if any are in paid invoices
          final paidMileage =
              allMileage
                  .where((expense) => expense.invoicedInId != null)
                  .toList();

          if (mounted) {
            if (paidMileage.isNotEmpty &&
                allMileage.length == paidMileage.length) {
              // All mileage expenses are in paid invoices
              ErrorDisplay.showInfo(
                context,
                'All mileage items for this job are already included in paid invoices',
              );
            } else {
              ErrorDisplay.showInfo(
                context,
                'No uninvoiced mileage items available',
              );
            }
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error loading mileage items: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        // Update availability flags
        await _checkAvailableRecords();

        // Loading state managed by LoadingStateProvider
      }
    }
  }

  void _addAllUninvoicedExpenses() async {
    if (_selectedJobId == null) return;

    // Loading state managed by LoadingStateProvider

    try {
      final jobItemsMap = await _supabaseService.generateInvoiceItemsFromJob(
        _selectedJobId!,
      );

      // We'll extract source IDs from the descriptions

      final List<InvoiceItem> availableItems =
          (jobItemsMap['availableItems'] as List<dynamic>).cast<InvoiceItem>();

      // Filter for expense items only
      final expenseItems =
          availableItems.where((item) => item.type == 'expense').toList();

      if (expenseItems.isNotEmpty) {
        // Track how many items were actually added (not already in the list)
        int addedCount = 0;

        setState(() {
          // Add expense items that aren't already in the line items
          for (final expenseItem in expenseItems) {
            bool alreadyExists = _lineItems.any(
              (existingItem) =>
                  existingItem.type == expenseItem.type &&
                  existingItem.description == expenseItem.description &&
                  (existingItem.unitPrice - expenseItem.unitPrice).abs() <
                      0.01 &&
                  (existingItem.quantity - expenseItem.quantity).abs() < 0.01,
            );

            if (!alreadyExists) {
              // Extract source ID from the description
              String? sourceId;

              // Description format: "Expense: {description}"
              final description = expenseItem.description;

              if (description.startsWith('Expense: ')) {
                // Extract the expense description
                final expenseDesc = description.substring('Expense: '.length);
                // Use a hash of the description and amount as the source ID
                final hashCode =
                    (expenseDesc + expenseItem.unitPrice.toString()).hashCode
                        .abs()
                        .toString();
                sourceId = 'expense_$hashCode';
              }

              // Add the item with the source ID
              _lineItems.add(expenseItem.copyWith(sourceId: sourceId));
              addedCount++;
            }
          }

          if (mounted) {
            if (addedCount > 0) {
              ErrorDisplay.showOperation(
                context,
                'Added $addedCount expense items',
              );
            } else {
              ErrorDisplay.showInfo(
                context,
                'All available expense items are already in the invoice',
              );
            }
          }
        });
      } else {
        if (mounted) {
          // Get all expenses for this job
          final allExpenses = await _supabaseService.getExpensesByJob(
            _selectedJobId!,
          );

          // Filter for non-mileage expenses
          final allRegularExpenses =
              allExpenses
                  .where(
                    (expense) => expense is! Mileage && !expense.isOverhead,
                  )
                  .toList();

          // Check if any are in paid invoices
          final paidExpenses =
              allRegularExpenses
                  .where((expense) => expense.invoicedInId != null)
                  .toList();

          if (mounted) {
            if (paidExpenses.isNotEmpty &&
                allRegularExpenses.length == paidExpenses.length) {
              // All regular expenses are in paid invoices
              ErrorDisplay.showInfo(
                context,
                'All expense items for this job are already included in paid invoices',
              );
            } else {
              ErrorDisplay.showInfo(
                context,
                'No uninvoiced expense items available',
              );
            }
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error loading expense items: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        // Update availability flags
        await _checkAvailableRecords();

        // Loading state managed by LoadingStateProvider
      }
    }
  }

  // Bulk action methods for adding items from open invoices
  void _addAllOpenInvoiceHours() async {
    if (_selectedJobId == null) return;

    // Loading state managed by LoadingStateProvider

    try {
      final jobItemsMap = await _supabaseService.generateInvoiceItemsFromJob(
        _selectedJobId!,
      );

      // We'll extract source IDs from the descriptions

      final List<InvoiceItem> openInvoiceItems =
          (jobItemsMap['inOpenInvoiceItems'] as List<dynamic>)
              .cast<InvoiceItem>();

      // Filter for labor items only
      final laborItems =
          openInvoiceItems.where((item) => item.type == 'labor').toList();

      if (laborItems.isNotEmpty) {
        // Track how many items were actually added (not already in the list)
        int addedCount = 0;

        setState(() {
          // Add labor items that aren't already in the line items
          for (final laborItem in laborItems) {
            bool alreadyExists = _lineItems.any(
              (existingItem) =>
                  existingItem.type == laborItem.type &&
                  existingItem.description == laborItem.description &&
                  (existingItem.unitPrice - laborItem.unitPrice).abs() < 0.01 &&
                  (existingItem.quantity - laborItem.quantity).abs() < 0.01,
            );

            if (!alreadyExists) {
              // Extract source ID from the description
              String? sourceId;

              // Description format: "Labor: Time log {id_substring} (In Invoice #{invoice_number})"
              // or "Labor: {notes} (In Invoice #{invoice_number})"
              final description = laborItem.description;
              final timeLogIdMatch = RegExp(
                r'Time log ([a-zA-Z0-9]{8})',
              ).firstMatch(description);

              if (timeLogIdMatch != null && timeLogIdMatch.groupCount >= 1) {
                // This is the ID substring (first 8 chars)
                final idSubstring = timeLogIdMatch.group(1);
                // We'll use this as the source ID with a prefix
                sourceId = 'timelog_$idSubstring';
              }

              // Add the item with the source ID and the pending invoice ID
              _lineItems.add(
                laborItem.copyWith(
                  sourceId: sourceId,
                  pendingInvoiceId: laborItem.pendingInvoiceId,
                ),
              );
              addedCount++;
            }
          }

          if (mounted) {
            if (addedCount > 0) {
              ErrorDisplay.showOperation(
                context,
                'Added $addedCount labor items from open invoices',
              );
            } else {
              ErrorDisplay.showInfo(
                context,
                'All labor items from open invoices are already in this invoice',
              );
            }
          }
        });
      } else {
        if (mounted) {
          ErrorDisplay.showInfo(
            context,
            'No labor items in open invoices available',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error loading labor items: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        // Update availability flags
        await _checkAvailableRecords();

        // Loading state managed by LoadingStateProvider
      }
    }
  }

  void _addAllOpenInvoiceMileage() async {
    if (_selectedJobId == null) return;

    // Loading state managed by LoadingStateProvider

    try {
      final jobItemsMap = await _supabaseService.generateInvoiceItemsFromJob(
        _selectedJobId!,
      );

      // We'll extract source IDs from the descriptions

      final List<InvoiceItem> openInvoiceItems =
          (jobItemsMap['inOpenInvoiceItems'] as List<dynamic>)
              .cast<InvoiceItem>();

      // Filter for mileage items only
      final mileageItems =
          openInvoiceItems.where((item) => item.type == 'mileage').toList();

      if (mileageItems.isNotEmpty) {
        // Track how many items were actually added (not already in the list)
        int addedCount = 0;

        setState(() {
          // Add mileage items that aren't already in the line items
          for (final mileageItem in mileageItems) {
            bool alreadyExists = _lineItems.any(
              (existingItem) =>
                  existingItem.type == mileageItem.type &&
                  existingItem.description == mileageItem.description &&
                  (existingItem.unitPrice - mileageItem.unitPrice).abs() <
                      0.01 &&
                  (existingItem.quantity - mileageItem.quantity).abs() < 0.01,
            );

            if (!alreadyExists) {
              // Extract source ID from the description
              String? sourceId;

              // Description format: "Mileage: {description} (In Invoice #{invoice_number})" or
              // "Mileage: Total for {count} trips ({miles} miles) (In Invoice #{invoice_number})"
              final description = mileageItem.description;

              // For individual mileage entries
              if (description.startsWith('Mileage: ')) {
                // Extract the invoice number
                final invoiceNumberMatch = RegExp(
                  r'In Invoice #([a-zA-Z0-9]+)',
                ).firstMatch(description);
                String invoiceNumber = '';
                if (invoiceNumberMatch != null &&
                    invoiceNumberMatch.groupCount >= 1) {
                  invoiceNumber = invoiceNumberMatch.group(1) ?? '';
                }

                // Use a hash of the description and invoice number as the source ID
                final hashCode =
                    (description + invoiceNumber).hashCode.abs().toString();
                sourceId = 'mileage_$hashCode';
              }

              // Add the item with the source ID and the pending invoice ID
              _lineItems.add(
                mileageItem.copyWith(
                  sourceId: sourceId,
                  pendingInvoiceId: mileageItem.pendingInvoiceId,
                ),
              );
              addedCount++;
            }
          }

          if (mounted) {
            if (addedCount > 0) {
              ErrorDisplay.showOperation(
                context,
                'Added $addedCount mileage items from open invoices',
              );
            } else {
              ErrorDisplay.showInfo(
                context,
                'All mileage items from open invoices are already in this invoice',
              );
            }
          }
        });
      } else {
        if (mounted) {
          ErrorDisplay.showInfo(
            context,
            'No mileage items in open invoices available',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error loading mileage items: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        // Update availability flags
        await _checkAvailableRecords();

        // Loading state managed by LoadingStateProvider
      }
    }
  }

  void _addAllOpenInvoiceExpenses() async {
    if (_selectedJobId == null) return;

    // Loading state managed by LoadingStateProvider

    try {
      final jobItemsMap = await _supabaseService.generateInvoiceItemsFromJob(
        _selectedJobId!,
      );

      // We'll extract source IDs from the descriptions

      final List<InvoiceItem> openInvoiceItems =
          (jobItemsMap['inOpenInvoiceItems'] as List<dynamic>)
              .cast<InvoiceItem>();

      // Filter for expense items only
      final expenseItems =
          openInvoiceItems.where((item) => item.type == 'expense').toList();

      if (expenseItems.isNotEmpty) {
        // Track how many items were actually added (not already in the list)
        int addedCount = 0;

        setState(() {
          // Add expense items that aren't already in the line items
          for (final expenseItem in expenseItems) {
            bool alreadyExists = _lineItems.any(
              (existingItem) =>
                  existingItem.type == expenseItem.type &&
                  existingItem.description == expenseItem.description &&
                  (existingItem.unitPrice - expenseItem.unitPrice).abs() <
                      0.01 &&
                  (existingItem.quantity - expenseItem.quantity).abs() < 0.01,
            );

            if (!alreadyExists) {
              // Extract source ID from the description
              String? sourceId;

              // Description format: "Expense: {description} (In Invoice #{invoice_number})"
              final description = expenseItem.description;

              if (description.startsWith('Expense: ')) {
                // Extract the invoice number
                final invoiceNumberMatch = RegExp(
                  r'In Invoice #([a-zA-Z0-9]+)',
                ).firstMatch(description);
                String invoiceNumber = '';
                if (invoiceNumberMatch != null &&
                    invoiceNumberMatch.groupCount >= 1) {
                  invoiceNumber = invoiceNumberMatch.group(1) ?? '';
                }

                // Extract the expense description
                String expenseDesc = description;
                if (description.contains('(In Invoice #')) {
                  expenseDesc =
                      description
                          .substring(0, description.indexOf('(In Invoice #'))
                          .trim();
                }

                // Use a hash of the description, amount, and invoice number as the source ID
                final hashCode =
                    (expenseDesc +
                            expenseItem.unitPrice.toString() +
                            invoiceNumber)
                        .hashCode
                        .abs()
                        .toString();
                sourceId = 'expense_$hashCode';
              }

              // Add the item with the source ID and the pending invoice ID
              _lineItems.add(
                expenseItem.copyWith(
                  sourceId: sourceId,
                  pendingInvoiceId: expenseItem.pendingInvoiceId,
                ),
              );
              addedCount++;
            }
          }

          if (mounted) {
            if (addedCount > 0) {
              ErrorDisplay.showOperation(
                context,
                'Added $addedCount expense items from open invoices',
              );
            } else {
              ErrorDisplay.showInfo(
                context,
                'All expense items from open invoices are already in this invoice',
              );
            }
          }
        });
      } else {
        if (mounted) {
          ErrorDisplay.showInfo(
            context,
            'No expense items in open invoices available',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'loadExpenseItems',
            'screen': 'InvoiceFormScreen',
          },
        );
        ErrorHandler.logError(appError);
        ErrorDisplay.showSnackBar(context, appError);
      }
    } finally {
      if (mounted) {
        // Update availability flags
        await _checkAvailableRecords();

        // Loading state managed by LoadingStateProvider
      }
    }
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      setState(() {
        _isRecording = true;
      });

      // Show feedback to indicate recording has started
      if (mounted) {
        ErrorDisplay.showInfo(
          context,
          'Recording started. Speak clearly to add invoice details.',
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to start recording: ${e.toString()}';
      });
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Show processing message
        if (mounted) {
          ErrorDisplay.showInfo(context, 'Processing your voice input...');
        }

        // Process the transcribed text
        final extractedInfo = _voiceRecordingService.processTranscribedText(
          transcribedText,
        );

        // Update form fields with extracted information
        setState(() {
          if (extractedInfo.containsKey('description') &&
              _notesController.text.isEmpty) {
            _notesController.text = extractedInfo['description'];
          }

          if (extractedInfo.containsKey('amount') &&
              _totalAmountController.text == '0.00') {
            _totalAmountController.text = extractedInfo['amount'];
          }

          if (extractedInfo.containsKey('jobName') && _selectedJobId == null) {
            // Try to find a job with a matching name
            final jobName = extractedInfo['jobName'];
            Job? matchingJob;
            try {
              matchingJob = _jobs.firstWhere(
                (job) =>
                    job.title.toLowerCase().contains(jobName.toLowerCase()),
              );
            } catch (e) {
              // No matching job found
              matchingJob = _jobs.isNotEmpty ? _jobs.first : null;
            }

            if (matchingJob != null) {
              _selectedJobId = matchingJob.id;
              _selectedCustomerId = matchingJob.customerId;
              _selectedJob = matchingJob;

              // Show success message for job selection
              if (mounted) {
                ErrorDisplay.showOperation(
                  context,
                  'Selected job: ${matchingJob.title}',
                );
              }
            }
          }
        });

        // Show what was recognized
        if (mounted && extractedInfo.isNotEmpty) {
          String recognizedInfo = 'Recognized: ';
          if (extractedInfo.containsKey('description')) {
            recognizedInfo += extractedInfo['description'];
          }
          if (extractedInfo.containsKey('amount')) {
            recognizedInfo += ' - Amount: \$${extractedInfo['amount']}';
          }

          ErrorDisplay.showOperation(context, recognizedInfo);
        }

        // Upload the audio file to Supabase Storage
        final recordId = widget.invoice?.id ?? const Uuid().v4();
        final recordingPath = _voiceRecordingService.getRecordingPath();
        if (recordingPath != null && recordingPath.isNotEmpty) {
          _voiceNoteUrl = await _voiceRecordingService.uploadAudioToStorage(
            recordingPath,
            'invoices',
            recordId,
          );

          // Show success message for voice note
          if (_voiceNoteUrl != null && mounted) {
            ErrorDisplay.showOperation(
              context,
              'Voice note saved with invoice',
            );
          }
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to process recording: ${e.toString()}';
        _isRecording = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.invoice == null ? 'Create Invoice' : 'Edit Invoice'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Voice recording button in app bar
          IconButton(
            icon:
                _isRecording
                    ? const Icon(Icons.stop_circle, color: Colors.red)
                    : const Icon(Icons.mic),
            onPressed: _isRecording ? _stopRecording : _startRecording,
            tooltip:
                _isRecording
                    ? 'Stop recording'
                    : 'Add invoice details using voice',
          ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          if (loadingProvider.isLoading('initializeForm') ||
              loadingProvider.isLoading('loadJobDetails')) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_errorMessage != null) {
            return Center(
              child: Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.red),
              ),
            );
          }

          return Consumer<DisplaySettingsProvider>(
            builder: (context, displayProvider, child) {
              return Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(
                    displayProvider.isOfficeMode ? 12 : 16,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Job selection
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Job',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedJobId,
                        items:
                            _jobs
                                .map(
                                  (job) => DropdownMenuItem(
                                    value: job.id,
                                    child: Text(job.title),
                                  ),
                                )
                                .toList(),
                        validator:
                            (value) =>
                                InputValidators.validateRequired(value, 'Job'),
                        onChanged:
                            widget.jobId != null
                                ? null
                                : (value) async {
                                  setState(() {
                                    _selectedJobId = value;
                                    // Update customer ID based on selected job
                                    if (value != null) {
                                      final job = _jobs.firstWhere(
                                        (j) => j.id == value,
                                      );
                                      _selectedCustomerId = job.customerId;
                                      _selectedJob = job;
                                    }
                                  });

                                  // Check if we should sync job costs based on invoice status
                                  bool canSyncCosts =
                                      widget.invoice == null ||
                                      widget.invoice!.status.toLowerCase() ==
                                          'open';

                                  // If any sync option is enabled and we can sync costs, load job costs
                                  if (value != null &&
                                      canSyncCosts &&
                                      (_selectedJob!.liveCostSyncEnabled ||
                                          _selectedJob!.syncExpenses ||
                                          _selectedJob!.syncMileage ||
                                          _selectedJob!.syncLaborCosts ||
                                          _selectedJob!.syncEstimateItems)) {
                                    // Loading state managed by LoadingStateProvider

                                    try {
                                      final jobItemsMap = await _supabaseService
                                          .generateInvoiceItemsFromJob(value);
                                      final List<InvoiceItem> availableItems =
                                          (jobItemsMap['availableItems']
                                                  as List<dynamic>)
                                              .cast<InvoiceItem>();

                                      // Check for available records to update button visibility
                                      await _checkAvailableRecords();

                                      setState(() {
                                        if (availableItems.isNotEmpty) {
                                          // For existing invoices, show a notification
                                          if (widget.invoice != null) {
                                            if (_shouldUpdateLineItems(
                                              _lineItems,
                                              availableItems,
                                            )) {
                                              _lineItems = availableItems;
                                              ErrorDisplay.showOperation(
                                                context,
                                                'Invoice updated with latest job costs',
                                              );
                                            }
                                          } else {
                                            // For new invoices, just update silently
                                            _lineItems = availableItems;
                                          }
                                        }
                                        // Loading state managed by LoadingStateProvider
                                      });
                                    } catch (e) {
                                      setState(() {
                                        _errorMessage =
                                            'Failed to load job costs: ${e.toString()}';
                                        // Loading state managed by LoadingStateProvider
                                      });
                                    }
                                  }
                                },
                      ),
                      const SizedBox(height: 16),

                      // Customer info (read-only, based on job)
                      if (_selectedCustomerId != null) ...[
                        Text(
                          'Customer: ${_customers.firstWhere((c) => c.id == _selectedCustomerId, orElse: () => Customer(id: '', userId: '', name: 'Unknown')).name}',
                          style: const TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: 8),
                        if (_selectedJob?.liveCostSyncEnabled == true) ...[
                          Row(
                            children: [
                              Icon(
                                Icons.sync,
                                color: Theme.of(context).colorScheme.primary,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                'Live Job Cost Sync is enabled for this job',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                        ],
                        const SizedBox(height: 8),
                      ],

                      // Date selection
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () => _selectDate(context, true),
                              child: InputDecorator(
                                decoration: const InputDecoration(
                                  labelText: 'Issue Date',
                                  border: OutlineInputBorder(),
                                ),
                                child: Text(
                                  DateFormat('MM/dd/yyyy').format(_issueDate),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Due date toggle
                      SwitchListTile(
                        title: const Text('Set Due Date'),
                        value: _useDueDate,
                        onChanged: (value) {
                          setState(() {
                            _useDueDate = value;
                            if (value && _dueDate == null) {
                              // If enabling due date and it's not set, initialize it
                              _loadDefaultDueDate();
                            }
                          });
                        },
                      ),

                      // Only show due date picker if enabled
                      if (_useDueDate)
                        Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDate(context, false),
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Due Date',
                                    border: OutlineInputBorder(),
                                  ),
                                  child: Text(
                                    _dueDate != null
                                        ? DateFormat(
                                          'MM/dd/yyyy',
                                        ).format(_dueDate!)
                                        : 'Not set',
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      const SizedBox(height: 24),

                      // Line items section
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Line Items',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              ElevatedButton.icon(
                                onPressed: _addLineItem,
                                icon: const Icon(Icons.add),
                                label: const Text('Add Item'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Theme.of(context).colorScheme.primary,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          if (_selectedJobId != null) ...[
                            const SizedBox(height: 16),
                            Card(
                              child: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Bulk Actions',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Left column - Add Uninvoiced Items
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  const Text(
                                                    'Add Uninvoiced Items:',
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 4),
                                                  IconButton(
                                                    icon: Icon(
                                                      Icons.info_outline,
                                                      size: 16,
                                                      color:
                                                          Colors.blue.shade800,
                                                    ),
                                                    onPressed:
                                                        () =>
                                                            _showPaidInvoiceTooltip(
                                                              context,
                                                            ),
                                                    tooltip: 'About paid items',
                                                    padding: EdgeInsets.zero,
                                                    constraints:
                                                        const BoxConstraints(),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 8),
                                              Wrap(
                                                spacing: 8,
                                                runSpacing: 8,
                                                children: [
                                                  if (_selectedJob != null &&
                                                      _selectedJob!
                                                          .syncLaborCosts &&
                                                      _hasUninvoicedHours)
                                                    ElevatedButton.icon(
                                                      onPressed:
                                                          _addAllUninvoicedHours,
                                                      icon: const Icon(
                                                        Icons.timer,
                                                        size: 16,
                                                      ),
                                                      label: const Text(
                                                        'Hours',
                                                      ),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor:
                                                            Colors.blue,
                                                        foregroundColor:
                                                            Colors.white,
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 12,
                                                              vertical: 8,
                                                            ),
                                                        textStyle:
                                                            const TextStyle(
                                                              fontSize: 12,
                                                            ),
                                                      ),
                                                    ),
                                                  if (_selectedJob != null &&
                                                      _selectedJob!
                                                          .syncMileage &&
                                                      _hasUninvoicedMileage)
                                                    ElevatedButton.icon(
                                                      onPressed:
                                                          _addAllUninvoicedMileage,
                                                      icon: const Icon(
                                                        Icons.directions_car,
                                                        size: 16,
                                                      ),
                                                      label: const Text(
                                                        'Mileage',
                                                      ),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor:
                                                            Colors.green,
                                                        foregroundColor:
                                                            Colors.white,
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 12,
                                                              vertical: 8,
                                                            ),
                                                        textStyle:
                                                            const TextStyle(
                                                              fontSize: 12,
                                                            ),
                                                      ),
                                                    ),
                                                  if (_selectedJob != null &&
                                                      _selectedJob!
                                                          .syncExpenses &&
                                                      _hasUninvoicedExpenses)
                                                    ElevatedButton.icon(
                                                      onPressed:
                                                          _addAllUninvoicedExpenses,
                                                      icon: const Icon(
                                                        Icons.receipt_long,
                                                        size: 16,
                                                      ),
                                                      label: const Text(
                                                        'Expenses',
                                                      ),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor:
                                                            Colors.orange,
                                                        foregroundColor:
                                                            Colors.white,
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 12,
                                                              vertical: 8,
                                                            ),
                                                        textStyle:
                                                            const TextStyle(
                                                              fontSize: 12,
                                                            ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        // Right column - Add Items from Open Invoices
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  const Text(
                                                    'Add Items from Open Invoices:',
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 4),
                                                  IconButton(
                                                    icon: Icon(
                                                      Icons.info_outline,
                                                      size: 16,
                                                      color:
                                                          Colors
                                                              .orange
                                                              .shade800,
                                                    ),
                                                    onPressed:
                                                        () =>
                                                            _showOpenInvoiceTooltip(
                                                              context,
                                                            ),
                                                    tooltip:
                                                        'What does this mean?',
                                                    padding: EdgeInsets.zero,
                                                    constraints:
                                                        const BoxConstraints(),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 8),
                                              Wrap(
                                                spacing: 8,
                                                runSpacing: 8,
                                                children: [
                                                  if (_selectedJob != null &&
                                                      _selectedJob!
                                                          .syncLaborCosts &&
                                                      _hasOpenInvoiceHours)
                                                    ElevatedButton.icon(
                                                      onPressed:
                                                          _addAllOpenInvoiceHours,
                                                      icon: const Icon(
                                                        Icons.timer,
                                                        size: 16,
                                                      ),
                                                      label: const Text(
                                                        'Hours',
                                                      ),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor:
                                                            Colors
                                                                .blue
                                                                .shade300,
                                                        foregroundColor:
                                                            Colors.white,
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 12,
                                                              vertical: 8,
                                                            ),
                                                        textStyle:
                                                            const TextStyle(
                                                              fontSize: 12,
                                                            ),
                                                      ),
                                                    ),
                                                  if (_selectedJob != null &&
                                                      _selectedJob!
                                                          .syncMileage &&
                                                      _hasOpenInvoiceMileage)
                                                    ElevatedButton.icon(
                                                      onPressed:
                                                          _addAllOpenInvoiceMileage,
                                                      icon: const Icon(
                                                        Icons.directions_car,
                                                        size: 16,
                                                      ),
                                                      label: const Text(
                                                        'Mileage',
                                                      ),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor:
                                                            Colors
                                                                .green
                                                                .shade300,
                                                        foregroundColor:
                                                            Colors.white,
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 12,
                                                              vertical: 8,
                                                            ),
                                                        textStyle:
                                                            const TextStyle(
                                                              fontSize: 12,
                                                            ),
                                                      ),
                                                    ),
                                                  if (_selectedJob != null &&
                                                      _selectedJob!
                                                          .syncExpenses &&
                                                      _hasOpenInvoiceExpenses)
                                                    ElevatedButton.icon(
                                                      onPressed:
                                                          _addAllOpenInvoiceExpenses,
                                                      icon: const Icon(
                                                        Icons.receipt_long,
                                                        size: 16,
                                                      ),
                                                      label: const Text(
                                                        'Expenses',
                                                      ),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor:
                                                            Colors
                                                                .orange
                                                                .shade300,
                                                        foregroundColor:
                                                            Colors.white,
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 12,
                                                              vertical: 8,
                                                            ),
                                                        textStyle:
                                                            const TextStyle(
                                                              fontSize: 12,
                                                            ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Column(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: Colors.orange.shade50,
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                            border: Border.all(
                                              color: Colors.orange.shade200,
                                            ),
                                          ),
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.warning_amber_rounded,
                                                size: 16,
                                                color: Colors.orange.shade800,
                                              ),
                                              const SizedBox(width: 8),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: const [
                                                    Text(
                                                      'Items already in open invoices are highlighted in amber and marked with their invoice number.',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    SizedBox(height: 2),
                                                    Text(
                                                      'These items can still be added to this invoice, but will appear in multiple invoices. Use the bulk action buttons to add them if needed.',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.shade50,
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                            border: Border.all(
                                              color: Colors.blue.shade200,
                                            ),
                                          ),
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.info_outline,
                                                size: 16,
                                                color: Colors.blue.shade800,
                                              ),
                                              const SizedBox(width: 8),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: const [
                                                    Text(
                                                      'Items already in paid invoices are completely hidden from the available items list.',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    SizedBox(height: 2),
                                                    Text(
                                                      'This prevents double-billing customers for the same work or expenses. You cannot add items from paid invoices to new invoices.',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                          // Add explanation about time logs and mileage
                          if (_selectedJob != null &&
                              (_selectedJob!.syncLaborCosts ||
                                  _selectedJob!.syncExpenses))
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Card(
                                color: Colors.blue.shade50,
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Note about suggested items:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.blue.shade800,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      if (_selectedJob!.syncLaborCosts) ...[
                                        const Text(
                                          '• Time logs are for tracking time spent on a job and can be optionally added to invoices.',
                                          style: TextStyle(fontSize: 12),
                                        ),
                                        Row(
                                          children: [
                                            TextButton.icon(
                                              icon: const Icon(
                                                Icons.timer,
                                                size: 14,
                                              ),
                                              label: const Text(
                                                'Include All Hours',
                                                style: TextStyle(fontSize: 12),
                                              ),
                                              onPressed: () {
                                                setState(() {
                                                  // Update all labor items to be included
                                                  for (
                                                    int i = 0;
                                                    i < _lineItems.length;
                                                    i++
                                                  ) {
                                                    if (_lineItems[i].type ==
                                                        'labor') {
                                                      _lineItems[i] =
                                                          _lineItems[i]
                                                              .copyWith(
                                                                excluded: false,
                                                              );
                                                    }
                                                  }
                                                });
                                              },
                                              style: TextButton.styleFrom(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 0,
                                                    ),
                                                minimumSize: Size.zero,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            TextButton.icon(
                                              icon: const Icon(
                                                Icons.timer_off,
                                                size: 14,
                                              ),
                                              label: const Text(
                                                'Exclude All Hours',
                                                style: TextStyle(fontSize: 12),
                                              ),
                                              onPressed: () {
                                                setState(() {
                                                  // Update all labor items to be excluded
                                                  for (
                                                    int i = 0;
                                                    i < _lineItems.length;
                                                    i++
                                                  ) {
                                                    if (_lineItems[i].type ==
                                                        'labor') {
                                                      _lineItems[i] =
                                                          _lineItems[i]
                                                              .copyWith(
                                                                excluded: true,
                                                              );
                                                    }
                                                  }
                                                });
                                              },
                                              style: TextButton.styleFrom(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 0,
                                                    ),
                                                minimumSize: Size.zero,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                      if (_selectedJob!.syncMileage) ...[
                                        const Text(
                                          '• Mileage entries are primarily for tax purposes and can be optionally added to invoices.',
                                          style: TextStyle(fontSize: 12),
                                        ),
                                        Row(
                                          children: [
                                            TextButton.icon(
                                              icon: const Icon(
                                                Icons.directions_car,
                                                size: 14,
                                              ),
                                              label: const Text(
                                                'Include All Mileage',
                                                style: TextStyle(fontSize: 12),
                                              ),
                                              onPressed: () {
                                                setState(() {
                                                  // Update all mileage items to be included
                                                  for (
                                                    int i = 0;
                                                    i < _lineItems.length;
                                                    i++
                                                  ) {
                                                    if (_lineItems[i].type ==
                                                        'mileage') {
                                                      _lineItems[i] =
                                                          _lineItems[i]
                                                              .copyWith(
                                                                excluded: false,
                                                              );
                                                    }
                                                  }
                                                });
                                              },
                                              style: TextButton.styleFrom(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 0,
                                                    ),
                                                minimumSize: Size.zero,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            TextButton.icon(
                                              icon: Icon(
                                                Icons.do_not_disturb,
                                                size: 14,
                                                color: Colors.red.shade300,
                                              ),
                                              label: const Text(
                                                'Exclude All Mileage',
                                                style: TextStyle(fontSize: 12),
                                              ),
                                              onPressed: () {
                                                setState(() {
                                                  // Update all mileage items to be excluded
                                                  for (
                                                    int i = 0;
                                                    i < _lineItems.length;
                                                    i++
                                                  ) {
                                                    if (_lineItems[i].type ==
                                                        'mileage') {
                                                      _lineItems[i] =
                                                          _lineItems[i]
                                                              .copyWith(
                                                                excluded: true,
                                                              );
                                                    }
                                                  }
                                                });
                                              },
                                              style: TextButton.styleFrom(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 0,
                                                    ),
                                                minimumSize: Size.zero,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                      if (_selectedJob!.syncExpenses) ...[
                                        const Text(
                                          '• Other expenses can be optionally added to invoices.',
                                          style: TextStyle(fontSize: 12),
                                        ),
                                        Row(
                                          children: [
                                            TextButton.icon(
                                              icon: const Icon(
                                                Icons.receipt_long,
                                                size: 14,
                                              ),
                                              label: const Text(
                                                'Include All Expenses',
                                                style: TextStyle(fontSize: 12),
                                              ),
                                              onPressed: () {
                                                setState(() {
                                                  // Update all expense items to be included
                                                  for (
                                                    int i = 0;
                                                    i < _lineItems.length;
                                                    i++
                                                  ) {
                                                    if (_lineItems[i].type ==
                                                        'expense') {
                                                      _lineItems[i] =
                                                          _lineItems[i]
                                                              .copyWith(
                                                                excluded: false,
                                                              );
                                                    }
                                                  }
                                                });
                                              },
                                              style: TextButton.styleFrom(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 0,
                                                    ),
                                                minimumSize: Size.zero,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            TextButton.icon(
                                              icon: Icon(
                                                Icons.do_not_disturb,
                                                size: 14,
                                                color: Colors.red.shade300,
                                              ),
                                              label: const Text(
                                                'Exclude All Expenses',
                                                style: TextStyle(fontSize: 12),
                                              ),
                                              onPressed: () {
                                                setState(() {
                                                  // Update all expense items to be excluded
                                                  for (
                                                    int i = 0;
                                                    i < _lineItems.length;
                                                    i++
                                                  ) {
                                                    if (_lineItems[i].type ==
                                                        'expense') {
                                                      _lineItems[i] =
                                                          _lineItems[i]
                                                              .copyWith(
                                                                excluded: true,
                                                              );
                                                    }
                                                  }
                                                });
                                              },
                                              style: TextButton.styleFrom(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 0,
                                                    ),
                                                minimumSize: Size.zero,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                      const Text(
                                        '• Use the include/exclude toggle (+ or - icon) to choose which items to include in this invoice.',
                                        style: TextStyle(fontSize: 12),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          const SizedBox(height: 8),
                          if (_lineItems.isEmpty)
                            const Card(
                              child: Padding(
                                padding: EdgeInsets.all(16),
                                child: Text('No line items added yet'),
                              ),
                            ),
                        ],
                      ),
                      if (_lineItems.isNotEmpty)
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(8),
                            child: Column(
                              children: [
                                // Header row
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                  child: Row(
                                    children: const [
                                      Expanded(
                                        flex: 3,
                                        child: Text(
                                          'Description',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          'Qty',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          'Price',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.right,
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          'Total',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.right,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 60,
                                      ), // Space for action buttons
                                    ],
                                  ),
                                ),
                                const Divider(),
                                // Line items
                                ...List.generate(_lineItems.length, (index) {
                                  final item = _lineItems[index];
                                  final bool isSyncedItem =
                                      item.type == 'expense' ||
                                      item.type == 'labor';
                                  final bool isExcluded = item.excluded == true;

                                  // Check if this item is in an open invoice
                                  final bool isInOpenInvoice =
                                      item.pendingInvoiceId != null;

                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 8,
                                    ),
                                    child: Row(
                                      children: [
                                        // Item type indicator for synced items
                                        if (isSyncedItem) ...[
                                          Container(
                                            width: 4,
                                            height: 24,
                                            margin: const EdgeInsets.only(
                                              right: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color:
                                                  isExcluded
                                                      ? Colors.grey
                                                      : (item.type == 'expense'
                                                          ? Colors.orange
                                                          : (item.type ==
                                                                  'mileage'
                                                              ? Colors.green
                                                              : Colors.blue)),
                                              borderRadius:
                                                  BorderRadius.circular(2),
                                            ),
                                          ),
                                          SizedBox(
                                            width: 20,
                                            child: Icon(
                                              item.type == 'expense'
                                                  ? Icons.receipt_long
                                                  : (item.type == 'mileage'
                                                      ? Icons.directions_car
                                                      : Icons.timer),
                                              size: 16,
                                              color:
                                                  isExcluded
                                                      ? Colors.grey
                                                      : (item.type == 'expense'
                                                          ? Colors.orange
                                                          : (item.type ==
                                                                  'mileage'
                                                              ? Colors.green
                                                              : Colors.blue)),
                                            ),
                                          ),
                                        ] else ...[
                                          const SizedBox(width: 28),
                                        ],
                                        Expanded(
                                          flex: 3,
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                item.description,
                                                style: TextStyle(
                                                  decoration:
                                                      isExcluded
                                                          ? TextDecoration
                                                              .lineThrough
                                                          : null,
                                                  color:
                                                      isExcluded
                                                          ? Colors.grey
                                                          : null,
                                                ),
                                              ),
                                              if (isInOpenInvoice)
                                                Row(
                                                  children: [
                                                    Container(
                                                      padding:
                                                          const EdgeInsets.symmetric(
                                                            horizontal: 4,
                                                            vertical: 2,
                                                          ),
                                                      decoration: BoxDecoration(
                                                        color:
                                                            Colors
                                                                .orange
                                                                .shade100,
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              4,
                                                            ),
                                                        border: Border.all(
                                                          color:
                                                              Colors
                                                                  .orange
                                                                  .shade800,
                                                          width: 0.5,
                                                        ),
                                                      ),
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Icon(
                                                            Icons
                                                                .warning_amber_rounded,
                                                            size: 12,
                                                            color:
                                                                Colors
                                                                    .orange
                                                                    .shade800,
                                                          ),
                                                          const SizedBox(
                                                            width: 4,
                                                          ),
                                                          Text(
                                                            'In open invoice #${item.pendingInvoiceId?.substring(0, 8) ?? ""}',
                                                            style: TextStyle(
                                                              fontSize: 10,
                                                              color:
                                                                  Colors
                                                                      .orange
                                                                      .shade800,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                            ],
                                          ),
                                        ),
                                        Expanded(
                                          flex: 1,
                                          child: Text(
                                            item.quantity.toString(),
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color:
                                                  isExcluded
                                                      ? Colors.grey
                                                      : null,
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 1,
                                          child: Text(
                                            '\$${item.unitPrice.toStringAsFixed(2)}',
                                            textAlign: TextAlign.right,
                                            style: TextStyle(
                                              color:
                                                  isExcluded
                                                      ? Colors.grey
                                                      : null,
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 1,
                                          child: Text(
                                            '\$${(item.quantity * item.unitPrice).toStringAsFixed(2)}',
                                            textAlign: TextAlign.right,
                                            style: TextStyle(
                                              color:
                                                  isExcluded
                                                      ? Colors.grey
                                                      : null,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          width:
                                              90, // Wider to accommodate the exclude toggle
                                          child: Row(
                                            children: [
                                              // Toggle for synced items to exclude/include
                                              if (isSyncedItem)
                                                IconButton(
                                                  icon: Icon(
                                                    isExcluded
                                                        ? Icons
                                                            .add_circle_outline
                                                        : Icons
                                                            .remove_circle_outline,
                                                    color:
                                                        isExcluded
                                                            ? Colors.green
                                                            : Colors.red,
                                                    size: 20,
                                                  ),
                                                  onPressed: () {
                                                    setState(() {
                                                      _lineItems[index] = item
                                                          .copyWith(
                                                            excluded:
                                                                !isExcluded,
                                                          );
                                                    });
                                                  },
                                                  tooltip:
                                                      isExcluded
                                                          ? 'Include item'
                                                          : 'Exclude item',
                                                  padding: EdgeInsets.zero,
                                                  constraints:
                                                      const BoxConstraints(),
                                                ),
                                              const SizedBox(width: 4),
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.edit,
                                                  size: 18,
                                                ),
                                                onPressed:
                                                    () => _editLineItem(index),
                                                padding: EdgeInsets.zero,
                                                constraints:
                                                    const BoxConstraints(),
                                              ),
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.delete,
                                                  size: 18,
                                                  color: Colors.red,
                                                ),
                                                onPressed:
                                                    () =>
                                                        _removeLineItem(index),
                                                padding: EdgeInsets.zero,
                                                constraints:
                                                    const BoxConstraints(),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }),
                                Divider(),
                                // Total row
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                  child: Row(
                                    children: [
                                      const Expanded(
                                        flex: 5,
                                        child: Text(
                                          'Calculated Total',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.right,
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          '\$${_lineItems.fold<double>(0, (sum, item) => item.excluded == true ? sum : sum + (item.quantity * item.unitPrice)).toStringAsFixed(2)}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.right,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 60,
                                      ), // Space for action buttons
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      SizedBox(height: 24),

                      // Manual Total Amount field
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _totalAmountController,
                              decoration: InputDecoration(
                                labelText: 'Invoice Total (\$)',
                                border: const OutlineInputBorder(),
                                hintText: 'Enter custom total amount',
                                enabled: _useManualTotal,
                                fillColor:
                                    _useManualTotal
                                        ? Colors.white
                                        : Colors.grey.shade200,
                                filled: true,
                                suffixIcon:
                                    _useManualTotal
                                        ? const Icon(Icons.edit)
                                        : null,
                              ),
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                    decimal: true,
                                  ),
                              enabled: _useManualTotal,
                              validator:
                                  (value) =>
                                      _useManualTotal
                                          ? InputValidators.validateCurrency(
                                            value,
                                            required: true,
                                            fieldName: 'Total amount',
                                            min: 0.0,
                                          )
                                          : null,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Switch(
                            value: _useManualTotal,
                            onChanged: (value) {
                              setState(() {
                                _useManualTotal = value;
                                // Always update the field with calculated value when toggling
                                final calculatedTotal = _lineItems.fold<double>(
                                  0,
                                  (sum, item) =>
                                      item.excluded == true
                                          ? sum
                                          : sum +
                                              (item.quantity * item.unitPrice),
                                );
                                _totalAmountController.text = calculatedTotal
                                    .toStringAsFixed(2);
                              });
                            },
                            activeColor: Theme.of(context).colorScheme.primary,
                          ),
                          Text(
                            'Edit Total',
                            style: TextStyle(
                              color:
                                  _useManualTotal
                                      ? Theme.of(context).colorScheme.primary
                                      : Colors.grey,
                              fontWeight:
                                  _useManualTotal
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),

                      // Notes field with voice recording button
                      Stack(
                        alignment: Alignment.centerRight,
                        children: [
                          TextFormField(
                            controller: _notesController,
                            decoration: InputDecoration(
                              labelText: 'Notes',
                              border: const OutlineInputBorder(),
                              alignLabelWithHint: true,
                              suffixIcon:
                                  _isRecording
                                      ? Container(
                                        margin: const EdgeInsets.only(
                                          right: 32,
                                        ),
                                        child: const Icon(
                                          Icons.mic,
                                          color: Colors.red,
                                        ),
                                      )
                                      : null,
                            ),
                            maxLines: 3,
                          ),
                          Positioned(
                            right: 8,
                            child: IconButton(
                              icon: Icon(
                                _isRecording ? Icons.stop : Icons.mic,
                                color:
                                    _isRecording
                                        ? Colors.red
                                        : Theme.of(context).colorScheme.primary,
                              ),
                              onPressed:
                                  _isRecording
                                      ? _stopRecording
                                      : _startRecording,
                              tooltip:
                                  _isRecording
                                      ? 'Stop recording'
                                      : 'Record voice note',
                            ),
                          ),
                        ],
                      ),
                      if (_isRecording)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            'Recording... Speak clearly to add notes or details about this invoice.',
                            style: TextStyle(
                              color: Colors.red,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      SizedBox(height: 24),

                      // Save button
                      Consumer<LoadingStateProvider>(
                        builder: (context, loadingProvider, child) {
                          return SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: ElevatedButton(
                              onPressed:
                                  loadingProvider.isLoading('saveInvoice')
                                      ? null
                                      : _saveInvoice,
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    Theme.of(context).colorScheme.primary,
                                foregroundColor: Colors.white,
                              ),
                              child:
                                  loadingProvider.isLoading('saveInvoice')
                                      ? const CircularProgressIndicator(
                                        color: Colors.white,
                                      )
                                      : Text(
                                        widget.invoice == null
                                            ? 'Create Invoice'
                                            : 'Update Invoice',
                                      ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  // Helper method already exists elsewhere in the class

  // Helper method to show a tooltip explaining what items in paid invoices are not shown
  void _showPaidInvoiceTooltip(BuildContext context) {
    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    // Create an overlay entry for the tooltip
    OverlayEntry? entry;
    entry = OverlayEntry(
      builder:
          (context) => Positioned(
            top: position.dy + 50,
            left: position.dx + 20,
            child: Material(
              elevation: 4.0,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.all(16),
                constraints: const BoxConstraints(maxWidth: 300),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade800, width: 1),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue.shade800),
                        const SizedBox(width: 8),
                        const Text(
                          'Items in Paid Invoices',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Items that have already been included in paid invoices are not shown in the list of available items. '
                      'This prevents double-billing customers for the same work or expenses.',
                    ),
                    const SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () {
                          entry?.remove();
                        },
                        child: const Text('Close'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );

    // Add the overlay entry
    overlay.insert(entry);

    // Remove the tooltip after 10 seconds if not closed manually
    Future.delayed(const Duration(seconds: 10), () {
      entry?.remove();
    });
  }

  // Helper method to show a tooltip explaining what it means when an item is in an open invoice
  void _showOpenInvoiceTooltip(BuildContext context) {
    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    // Create an overlay entry for the tooltip
    OverlayEntry? entry;
    entry = OverlayEntry(
      builder:
          (context) => Positioned(
            top: position.dy + 50,
            left: position.dx + 20,
            child: Material(
              elevation: 4.0,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.all(16),
                constraints: const BoxConstraints(maxWidth: 300),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade800, width: 1),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.orange.shade800),
                        const SizedBox(width: 8),
                        const Text(
                          'Items in Open Invoices',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'These items are already included in other unpaid (open) invoices. '
                      'You can still add them to this invoice, but be aware that they will '
                      'appear in multiple invoices.',
                    ),
                    const SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () {
                          entry?.remove();
                        },
                        child: const Text('Close'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );

    // Add the overlay entry
    overlay.insert(entry);

    // Remove the tooltip after 10 seconds if not closed manually
    Future.delayed(const Duration(seconds: 10), () {
      entry?.remove();
    });
  }
}
