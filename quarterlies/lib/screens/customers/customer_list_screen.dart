import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/responsive_helper.dart';
import 'package:quarterlies/providers/customer_provider.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'dart:async';
import 'customer_detail_screen.dart';
import 'customer_form_screen.dart';

class CustomerListScreen extends StatefulWidget {
  const CustomerListScreen({super.key});

  @override
  State<CustomerListScreen> createState() => _CustomerListScreenState();
}

class _CustomerListScreenState extends State<CustomerListScreen> {
  final stt.SpeechToText _speechToText =
      stt.SpeechToText(); // Added for voice input
  bool _isListening = false; // Tracks if voice input is active
  final DataRepository _dataRepository = DataRepository();
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    // Initialize customer provider data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(context, listen: false).refreshCustomers();
    });
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
          Provider.of<CustomerProvider>(
            context,
            listen: false,
          ).refreshCustomers(); // Reload data when back online
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  @override
  void dispose() {
    _speechToText.stop(); // Stop listening when the widget is disposed
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  void _startListening() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithVoiceLoading(
        () async {
          if (!_isListening && await _speechToText.initialize()) {
            setState(() => _isListening = true);
            _speechToText.listen(
              onResult: (result) {
                setState(() => _isListening = false);
                // Use CustomerProvider for search
                Provider.of<CustomerProvider>(
                  context,
                  listen: false,
                ).searchCustomers(result.recognizedWords);
              },
            );
          }
        },
        operationName: 'Voice search customers',
        isRecording: true,
      );
    } catch (e) {
      setState(() => _isListening = false);
    }
  }

  void _stopListening() {
    if (_isListening) {
      setState(() => _isListening = false);
      _speechToText.stop();
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, child) {
        return ResponsiveContainer(
          child: Scaffold(
            appBar: AppBar(
              title: const ResponsiveTitle(
                'Customers',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              elevation: spacing.ResponsiveSpacing.getElevation(context),
              toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
              actions: [
                // Offline status indicator
                if (customerProvider.isOffline)
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Tooltip(
                      message: 'You are offline. Using locally stored data.',
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.cloud_off,
                            color: Colors.white,
                            size: spacing.ResponsiveSpacing.getIconSize(
                              context,
                              base: 20.0,
                            ),
                          ),
                          SizedBox(
                            width: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 4.0,
                            ),
                          ),
                          const ResponsiveLabel(
                            'Offline',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                IconButton(
                  icon: Icon(
                    _isListening ? Icons.mic : Icons.mic_none,
                    size: spacing.ResponsiveSpacing.getIconSize(context),
                  ),
                  onPressed: _isListening ? _stopListening : _startListening,
                  tooltip: 'Voice Search',
                ),
                IconButton(
                  icon: Icon(
                    Icons.refresh,
                    size: spacing.ResponsiveSpacing.getIconSize(context),
                  ),
                  onPressed: () => customerProvider.refreshCustomers(),
                ),
              ],
            ),
            floatingActionButton: FloatingActionButton(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CustomerFormScreen(),
                  ),
                );
                if (result == true) {
                  customerProvider.refreshCustomers();
                }
              },
              child: const Icon(Icons.add),
            ),
            body: customerProvider.isLoading
                ? const Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading customers...',
                    size: 32.0,
                  ),
                )
                : customerProvider.errorMessage != null
                ? ResponsiveLayout(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: spacing.ResponsiveSpacing.getPadding(context),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(
                            color: Colors.red.shade200,
                          ),
                          borderRadius: BorderRadius.circular(
                            spacing.ResponsiveSpacing.getBorderRadius(context, base: 12.0),
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red.shade700,
                              size: spacing.ResponsiveSpacing.getIconSize(context, base: 48.0),
                            ),
                            SizedBox(height: spacing.ResponsiveSpacing.getSpacing(context)),
                            ResponsiveTitle(
                              'Unable to Load Customers',
                              style: TextStyle(
                                color: Colors.red.shade700,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: spacing.ResponsiveSpacing.getSpacing(context, base: 8.0)),
                            ResponsiveBody(
                              ErrorHandler.getUserFriendlyMessage(
                                AppError.fromException(
                                  Exception(
                                    customerProvider.errorMessage!,
                                  ),
                                  context: {
                                    'operation': 'loadCustomers',
                                  },
                                ),
                              ),
                              style: TextStyle(color: Colors.red.shade600),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: spacing.ResponsiveSpacing.getSpacing(context)),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ElevatedButton.icon(
                                  onPressed: () => customerProvider.refreshCustomers(),
                                  icon: Icon(
                                    Icons.refresh,
                                    size: spacing.ResponsiveSpacing.getIconSize(context, base: 20.0),
                                  ),
                                  label: const ResponsiveBody('Try Again'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red.shade700,
                                    foregroundColor: Colors.white,
                                    minimumSize: Size(
                                      double.infinity,
                                      spacing.ResponsiveSpacing.getButtonHeight(context),
                                    ),
                                  ),
                                ),
                                SizedBox(width: spacing.ResponsiveSpacing.getSpacing(context, base: 12.0)),
                                OutlinedButton.icon(
                                  onPressed: () async {
                                    final result = await Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => const CustomerFormScreen(),
                                      ),
                                    );
                                    if (result == true) {
                                      customerProvider.refreshCustomers();
                                    }
                                  },
                                  icon: Icon(
                                    Icons.add,
                                    size: spacing.ResponsiveSpacing.getIconSize(context, base: 20.0),
                                  ),
                                  label: const ResponsiveBody('Add Customer'),
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: Colors.red.shade700,
                                    side: BorderSide(color: Colors.red.shade700),
                                    minimumSize: Size(
                                      double.infinity,
                                      spacing.ResponsiveSpacing.getButtonHeight(context),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                : RefreshIndicator(
                        onRefresh: () => customerProvider.refreshCustomers(),
                        child:
                            customerProvider.filteredCustomers.isEmpty
                                ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.people_outline,
                                        size: 64,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary.withAlpha(179),
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'No customers found',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w500,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.onSurface,
                                        ),
                                      ),
                                      const SizedBox(height: 24),
                                      CustomButton(
                                        text: 'Add Customer',
                                        onPressed: () async {
                                          final result = await Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder:
                                                  (context) =>
                                                      const CustomerFormScreen(),
                                            ),
                                          );
                                          if (result == true) {
                                            customerProvider.refreshCustomers();
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                )
                                : Column(
                                  children: [
                                    // Search bar with voice indicator
                                    Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Column(
                                        children: [
                                          TextField(
                                            decoration: const InputDecoration(
                                              hintText:
                                                  'Search customers by name, email, or phone...',
                                              prefixIcon: Icon(Icons.search),
                                              border: OutlineInputBorder(),
                                            ),
                                            onChanged:
                                                (value) => customerProvider
                                                    .searchCustomers(value),
                                          ),
                                          // Voice loading indicator
                                          Consumer<LoadingStateProvider>(
                                            builder: (
                                              context,
                                              loadingProvider,
                                              child,
                                            ) {
                                              if (loadingProvider
                                                  .isProcessingVoice) {
                                                return Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                        top: 8.0,
                                                      ),
                                                  child: VoiceLoadingIndicator(
                                                    operation:
                                                        loadingProvider
                                                            .voiceOperation ??
                                                        'Processing voice...',
                                                    isRecording:
                                                        loadingProvider
                                                            .isRecording,
                                                  ),
                                                );
                                              }
                                              return const SizedBox.shrink();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    // Customer list
                                    Expanded(
                                      child: ListView.builder(
                                        itemCount:
                                            customerProvider
                                                .filteredCustomers
                                                .length,
                                        itemBuilder: (context, index) {
                                          final customer =
                                              customerProvider
                                                  .filteredCustomers[index];
                                          return Consumer<
                                            DisplaySettingsProvider
                                          >(
                                            builder: (
                                              context,
                                              displayProvider,
                                              child,
                                            ) {
                                              return AdaptiveListTile(
                                                title: Text(
                                                  customer.name,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                                subtitle: Text(
                                                  '${customer.email ?? 'No email'} • ${customer.phone ?? 'No phone'}',
                                                ),
                                                leading: CircleAvatar(
                                                  backgroundColor:
                                                      Theme.of(
                                                        context,
                                                      ).colorScheme.primary,
                                                  child: Text(
                                                    customer.name.isNotEmpty
                                                        ? customer.name[0]
                                                            .toUpperCase()
                                                        : 'C',
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                                trailing: const Icon(
                                                  Icons.chevron_right,
                                                ),
                                                // Additional info shown only in Office Mode
                                                additionalInfo:
                                                    displayProvider.isOfficeMode
                                                        ? OfficeAdditionalInfo(
                                                          items: [
                                                            if (customer.address !=
                                                                    null &&
                                                                customer
                                                                    .address!
                                                                    .isNotEmpty)
                                                              InfoItem(
                                                                label:
                                                                    'Address',
                                                                value:
                                                                    customer
                                                                        .address!,
                                                                icon:
                                                                    Icons
                                                                        .location_on,
                                                              ),
                                                            InfoItem(
                                                              label: 'Created',
                                                              value: _formatDate(
                                                                customer
                                                                    .createdAt,
                                                              ),
                                                              icon:
                                                                  Icons
                                                                      .calendar_today,
                                                            ),
                                                          ],
                                                        )
                                                        : null,
                                                // Office actions shown only in Office Mode
                                                officeActions:
                                                    displayProvider.isOfficeMode
                                                        ? [
                                                          OfficeActionButton(
                                                            icon: Icons.edit,
                                                            label: 'Edit',
                                                            onPressed: () async {
                                                              final result = await Navigator.push(
                                                                context,
                                                                MaterialPageRoute(
                                                                  builder:
                                                                      (
                                                                        context,
                                                                      ) => CustomerFormScreen(
                                                                        customer:
                                                                            customer,
                                                                      ),
                                                                ),
                                                              );
                                                              if (result ==
                                                                  true) {
                                                                customerProvider
                                                                    .refreshCustomers();
                                                              }
                                                            },
                                                          ),
                                                          OfficeActionButton(
                                                            icon: Icons.work,
                                                            label: 'Jobs',
                                                            onPressed: () {
                                                              // Navigate to jobs for this customer
                                                              ScaffoldMessenger.of(
                                                                context,
                                                              ).showSnackBar(
                                                                const SnackBar(
                                                                  content: Text(
                                                                    'Jobs view coming soon!',
                                                                  ),
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                          OfficeActionButton(
                                                            icon: Icons.receipt,
                                                            label: 'Invoices',
                                                            onPressed: () {
                                                              // Navigate to invoices for this customer
                                                              ScaffoldMessenger.of(
                                                                context,
                                                              ).showSnackBar(
                                                                const SnackBar(
                                                                  content: Text(
                                                                    'Invoices view coming soon!',
                                                                  ),
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                        ]
                                                        : null,
                                                onTap: () async {
                                                  final result =
                                                      await Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                          builder:
                                                              (context) =>
                                                                  CustomerDetailScreen(
                                                                    customerId:
                                                                        customer
                                                                            .id,
                                                                  ),
                                                        ),
                                                      );
                                                  if (result == true) {
                                                    customerProvider
                                                        .refreshCustomers();
                                                  }
                                                },
                                              );
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                      ),
            ),
          ),
        );
      },
    );
  }
}
